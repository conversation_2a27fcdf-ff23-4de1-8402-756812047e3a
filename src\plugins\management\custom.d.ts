declare module '@strapi/design-system/*';
declare module '@strapi/design-system';
declare module '@strapi/icons';
declare module '@strapi/icons/*';
declare module '@strapi/helper-plugin';

// Fix for recharts TypeScript issues in version 3.1.0
declare module 'recharts' {
  import { ComponentType, ReactElement, ReactNode } from 'react';

  // Base props interface
  interface BaseProps {
    [key: string]: any;
  }

  // ResponsiveContainer
  interface ResponsiveContainerProps extends BaseProps {
    width?: string | number;
    height?: string | number;
    children?: ReactNode;
  }
  export const ResponsiveContainer: ComponentType<ResponsiveContainerProps>;

  // Chart components
  interface ChartProps extends BaseProps {
    data?: any[];
    children?: ReactNode;
  }
  export const AreaChart: ComponentType<ChartProps>;
  export const BarChart: ComponentType<ChartProps>;
  export const LineChart: ComponentType<ChartProps>;
  export const PieChart: ComponentType<ChartProps>;

  // Axis components
  interface AxisProps extends BaseProps {
    dataKey?: string;
  }
  export const XAxis: ComponentType<AxisProps>;
  export const YAxis: ComponentType<AxisProps>;

  // Other components
  export const CartesianGrid: ComponentType<BaseProps>;
  export const Tooltip: ComponentType<BaseProps>;
  export const Legend: ComponentType<BaseProps>;
  export const Area: ComponentType<BaseProps>;
  export const Bar: ComponentType<BaseProps>;
  export const Line: ComponentType<BaseProps>;
  export const Pie: ComponentType<BaseProps>;
  export const Cell: ComponentType<BaseProps>;
}
