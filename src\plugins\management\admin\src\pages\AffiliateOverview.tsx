import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card as AntCard,
  Typography,
  Space,
  Select,
  Spin,
  message,
  Avatar,
  List,
  Tag,
} from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
  TeamOutlined,
  ReloadOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { useFetchClient } from '@strapi/helper-plugin';
import {
  PageContainer,
  Card,
  CardContent,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
} from '../components/shared';
import {
  DollarSign,
  ShoppingCart,
  Award,
  Users,
  TrendingUp,
  BarChart3,
} from 'lucide-react';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  AreaChart,
  Area,
  Cell,
} from 'recharts';

const { Title, Text } = Typography;
const { Option } = Select;

// Types
interface AffiliateStats {
  totalRevenue: number;
  totalOrders: number;
  paidCommissions: number;
  unpaidCommissions: number;
}

interface RevenueData {
  month: string;
  revenue: number;
  orders: number;
  commissions: number;
}

interface TopAffiliate {
  id: number;
  name: string;
  phone: string;
  email: string;
  avatar?: string;
  totalRevenue: number;
  totalOrders: number;
  totalCommissions: number;
  rank: number;
}

interface CommissionStatusData {
  status: string;
  count: number;
  amount: number;
  color: string;
}

const AffiliateOverview: React.FC = () => {
  const { get } = useFetchClient();

  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('month');
  const [stats, setStats] = useState<AffiliateStats | null>(null);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [topAffiliates, setTopAffiliates] = useState<TopAffiliate[]>([]);
  const [commissionStatusData, setCommissionStatusData] = useState<
    CommissionStatusData[]
  >([]);

  // Stats data for display
  const statsData = [
    {
      title: 'Tổng doanh thu từ đại lý',
      value: stats?.totalRevenue
        ? new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
          }).format(stats.totalRevenue)
        : '0 ₫',
      icon: DollarSign,
      color: '#3b82f6',
    },
    {
      title: 'Tổng đơn hàng từ đại lý',
      value: stats?.totalOrders || 0,
      icon: ShoppingCart,
      color: '#10b981',
    },
    {
      title: 'Hoa hồng đã chi',
      value: stats?.paidCommissions
        ? new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
          }).format(stats.paidCommissions)
        : '0 ₫',
      icon: Award,
      color: '#059669',
    },
    {
      title: 'Hoa hồng chưa chi',
      value: stats?.unpaidCommissions
        ? new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
          }).format(stats.unpaidCommissions)
        : '0 ₫',
      icon: Users,
      color: '#f59e0b',
    },
  ];

  // Sample data for charts (used as fallback when API data is not available)
  const sampleRevenueData = [
    { month: 'T1', revenue: 45000000, orders: 120, commissions: 2250000 },
    { month: 'T2', revenue: 52000000, orders: 140, commissions: 2600000 },
    { month: 'T3', revenue: 48000000, orders: 130, commissions: 2400000 },
    { month: 'T4', revenue: 61000000, orders: 165, commissions: 3050000 },
    { month: 'T5', revenue: 55000000, orders: 150, commissions: 2750000 },
    { month: 'T6', revenue: 67000000, orders: 180, commissions: 3350000 },
  ];

  const sampleTopAffiliates = [
    {
      id: 1,
      name: 'Nguyễn Thị Mai',
      phone: '0901234567',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 25000000,
      totalOrders: 85,
      totalCommissions: 1250000,
      rank: 1,
    },
    {
      id: 2,
      name: 'Trần Văn Minh',
      phone: '0912345678',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 22000000,
      totalOrders: 78,
      totalCommissions: 1100000,
      rank: 2,
    },
    {
      id: 3,
      name: 'Lê Thị Hương',
      phone: '0923456789',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 20000000,
      totalOrders: 72,
      totalCommissions: 1000000,
      rank: 3,
    },
    {
      id: 4,
      name: 'Phạm Quốc Anh',
      phone: '0934567890',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 18000000,
      totalOrders: 65,
      totalCommissions: 900000,
      rank: 4,
    },
    {
      id: 5,
      name: 'Hoàng Thị Lan',
      phone: '0945678901',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 16000000,
      totalOrders: 58,
      totalCommissions: 800000,
      rank: 5,
    },
  ];

  const fetchAffiliateStats = async () => {
    setLoading(true);
    try {
      // Fetch real data from APIs

      // 1. Get KPI data from dashboard
      const kpiResponse = await get('/management/dashboard/kpi');
      const kpiData = kpiResponse.data;

      // 2. Get commission statistics
      const commissionResponse = await get(
        '/management/commissions/statistics'
      );
      const commissionData = commissionResponse.data;

      // 3. Get top affiliates
      const topAffiliatesResponse = await get(
        '/management/dashboard/top-customers'
      );
      const topAffiliatesData = topAffiliatesResponse.data.data || [];

      // 4. Get chart data
      const chartResponse = await get('/management/dashboard/charts/revenue');
      const chartData = chartResponse.data.data || [];

      // Set stats with real data
      setStats({
        totalRevenue: kpiData.totalRevenue || 0,
        totalOrders: kpiData.totalOrders || 0,
        paidCommissions: commissionData.paidAmount || 0,
        unpaidCommissions: commissionData.pendingAmount || 0,
      });

      // Format chart data for revenue chart
      const formattedChartData = chartData.map((item: any, index: number) => ({
        month: `T${index + 1}`,
        revenue: item.revenue || 0,
        orders: item.orders || 0,
        commissions: Math.round((item.revenue || 0) * 0.05), // Assume 5% commission rate
      }));

      setRevenueData(
        formattedChartData.length > 0 ? formattedChartData : sampleRevenueData
      );

      // Format top affiliates data and get additional user info
      const formattedAffiliates = await Promise.all(
        topAffiliatesData.map(async (affiliate: any, index: number) => {
          let phone = 'N/A';

          // Try to get phone number from user API
          try {
            const userResponse = await get(
              `/management/users?search=${affiliate.email}&pageSize=1`
            );
            const userData = userResponse.data.data?.[0];
            if (userData?.phone) {
              phone = userData.phone;
            }
          } catch (error) {
            console.log('Could not fetch user phone for:', affiliate.email);
          }

          return {
            id: affiliate.id,
            name: affiliate.name || 'N/A',
            phone: phone,
            email: affiliate.email || 'N/A',
            avatar: '',
            totalRevenue: affiliate.totalSpent || 0,
            totalOrders: affiliate.totalOrders || 0,
            totalCommissions: Math.round((affiliate.totalSpent || 0) * 0.05), // Assume 5% commission
            rank: index + 1,
          };
        })
      );

      setTopAffiliates(
        formattedAffiliates.length > 0
          ? formattedAffiliates
          : sampleTopAffiliates
      );

      // Set commission status data
      const statusData: CommissionStatusData[] = [
        {
          status: 'Chờ duyệt',
          count: commissionData.pendingCommissions || 0,
          amount: commissionData.pendingAmount || 0,
          color: '#f59e0b',
        },
        {
          status: 'Đã duyệt',
          count: commissionData.paidCommissions || 0,
          amount: commissionData.paidAmount || 0,
          color: '#10b981',
        },
        {
          status: 'Đã hủy',
          count: commissionData.cancelledCommissions || 0,
          amount: commissionData.cancelledAmount || 0,
          color: '#ef4444',
        },
      ];

      setCommissionStatusData(statusData);
    } catch (error) {
      console.error('Error fetching affiliate stats:', error);
      message.error('Không thể tải dữ liệu thống kê');

      // Fallback to sample data on error
      setStats({
        totalRevenue: 348000000,
        totalOrders: 923,
        paidCommissions: 15400000,
        unpaidCommissions: 2600000,
      });
      setRevenueData(sampleRevenueData);
      setTopAffiliates(sampleTopAffiliates);

      // Set sample commission status data
      const sampleStatusData: CommissionStatusData[] = [
        {
          status: 'Chờ thanh toán',
          count: 45,
          amount: 2600000,
          color: '#f59e0b',
        },
        {
          status: 'Đã thanh toán',
          count: 128,
          amount: 15400000,
          color: '#10b981',
        },
        {
          status: 'Đã hủy',
          count: 8,
          amount: 400000,
          color: '#ef4444',
        },
      ];
      setCommissionStatusData(sampleStatusData);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchAffiliateStats();
  };

  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    fetchAffiliateStats();
  };

  useEffect(() => {
    fetchAffiliateStats();
  }, []);

  return (
    <PageContainer>
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Page Header */}
        <Card style={{ marginBottom: 24 }}>
          <PageHeader
            title="Affiliate - Tổng quan"
            description="Thống kê tổng quan về hoạt động affiliate và đại lý"
            actions={
              <Space>
                <Select
                  value={timeRange}
                  onChange={handleTimeRangeChange}
                  style={{ width: 120 }}
                >
                  <Option value="week">7 ngày</Option>
                  <Option value="month">30 ngày</Option>
                  <Option value="quarter">3 tháng</Option>
                  <Option value="year">1 năm</Option>
                </Select>
                <Button onClick={handleRefresh} $variant="outline">
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </Space>
            }
          />
        </Card>

        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* Charts Section */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          {/* Revenue Chart */}
          <Col xs={24} lg={12}>
            <Card>
              <CardContent>
                <div style={{ marginBottom: 16 }}>
                  <Title
                    level={4}
                    style={{
                      margin: 0,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                    }}
                  >
                    <Space>
                      <TrendingUp size={20} style={{ color: '#3b82f6' }} />
                      Thống kê doanh thu
                    </Space>
                  </Title>
                </div>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        name === 'revenue'
                          ? new Intl.NumberFormat('vi-VN', {
                              style: 'currency',
                              currency: 'VND',
                            }).format(Number(value))
                          : value,
                        name === 'revenue' ? 'Doanh thu' : 'Hoa hồng',
                      ]}
                    />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stackId="1"
                      stroke="#3b82f6"
                      fill="#3b82f6"
                      fillOpacity={0.6}
                      name="Doanh thu"
                    />
                    <Area
                      type="monotone"
                      dataKey="commissions"
                      stackId="2"
                      stroke="#10b981"
                      fill="#10b981"
                      fillOpacity={0.6}
                      name="Hoa hồng"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Col>

          {/* Orders Chart */}
          <Col xs={24} lg={12}>
            <Card>
              <CardContent>
                <div style={{ marginBottom: 16 }}>
                  <Title
                    level={4}
                    style={{
                      margin: 0,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                    }}
                  >
                    <Space>
                      <BarChart3 size={20} style={{ color: '#10b981' }} />
                      Đơn hàng
                    </Space>
                  </Title>
                </div>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="orders" fill="#10b981" name="Số đơn hàng" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Col>
        </Row>

        {/* Bottom Section */}
        <Row gutter={[16, 16]}>
          {/* Commission Status Chart */}
          <Col xs={24} lg={12}>
            <Card>
              <CardContent>
                <div style={{ marginBottom: 16 }}>
                  <Title
                    level={4}
                    style={{
                      margin: 0,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                    }}
                  >
                    <Space>
                      <Award size={20} style={{ color: '#f59e0b' }} />
                      Thống kê hoa hồng
                    </Space>
                  </Title>
                </div>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={commissionStatusData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis
                      dataKey="status"
                      tick={{ fontSize: 12, fill: '#64748b' }}
                      axisLine={{ stroke: '#e5e7eb' }}
                    />
                    <YAxis
                      tick={{ fontSize: 12, fill: '#64748b' }}
                      axisLine={{ stroke: '#e5e7eb' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#ffffff',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        fontFamily: "'Be Vietnam Pro', sans-serif",
                      }}
                      formatter={(value) => [`${value} hoa hồng`, 'Số lượng']}
                      labelFormatter={(label) => `Trạng thái: ${label}`}
                    />
                    <Bar dataKey="count" radius={[4, 4, 0, 0]} fill="#3b82f6">
                      {commissionStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Col>

          {/* Top Affiliates */}
          <Col xs={24} lg={12}>
            <Card>
              <CardContent>
                <div style={{ marginBottom: 16 }}>
                  <Title
                    level={4}
                    style={{
                      margin: 0,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                    }}
                  >
                    <Space>
                      <TrophyOutlined style={{ color: '#f59e0b' }} />
                      Top Cộng tác viên
                    </Space>
                  </Title>
                </div>
                <List
                  itemLayout="horizontal"
                  dataSource={topAffiliates}
                  renderItem={(affiliate) => (
                    <List.Item
                      style={{
                        padding: '12px 0',
                        borderBottom: '1px solid #f0f0f0',
                      }}
                    >
                      <List.Item.Meta
                        avatar={
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 8,
                            }}
                          >
                            <Tag
                              color={affiliate.rank <= 3 ? 'gold' : 'blue'}
                              style={{
                                margin: 0,
                                minWidth: 24,
                                textAlign: 'center',
                              }}
                            >
                              #{affiliate.rank}
                            </Tag>
                            <Avatar
                              src={affiliate.avatar}
                              size={40}
                              style={{ backgroundColor: '#f0f2f5' }}
                            >
                              {affiliate.name.charAt(0)}
                            </Avatar>
                          </div>
                        }
                        title={
                          <div
                            style={{
                              fontFamily: "'Be Vietnam Pro', sans-serif",
                            }}
                          >
                            <div style={{ fontWeight: 600, color: '#1e293b' }}>
                              {affiliate.name}
                            </div>
                            <div style={{ fontSize: '12px', color: '#64748b' }}>
                              {affiliate.phone} • {affiliate.email}
                            </div>
                          </div>
                        }
                        description={
                          <div
                            style={{
                              fontFamily: "'Be Vietnam Pro', sans-serif",
                            }}
                          >
                            <Space
                              split={
                                <span style={{ color: '#e5e7eb' }}>|</span>
                              }
                            >
                              <Text
                                style={{ fontSize: '12px', color: '#059669' }}
                              >
                                {new Intl.NumberFormat('vi-VN', {
                                  style: 'currency',
                                  currency: 'VND',
                                }).format(affiliate.totalRevenue)}
                              </Text>
                              <Text
                                style={{ fontSize: '12px', color: '#3b82f6' }}
                              >
                                {affiliate.totalOrders} đơn
                              </Text>
                              <Text
                                style={{ fontSize: '12px', color: '#f59e0b' }}
                              >
                                {new Intl.NumberFormat('vi-VN', {
                                  style: 'currency',
                                  currency: 'VND',
                                }).format(affiliate.totalCommissions)}
                              </Text>
                            </Space>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </CardContent>
            </Card>
          </Col>
        </Row>
      </Spin>
    </PageContainer>
  );
};

export default AffiliateOverview;
